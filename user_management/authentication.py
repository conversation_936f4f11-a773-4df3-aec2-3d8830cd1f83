from flask import Flask, Blueprint, render_template, request, redirect, url_for, session, jsonify,  flash, current_app
from itsdangerous import URLSafeTimedSerializer, SignatureExpired, BadSignature
from user_management.roles import has_role  # Role-checking function
from flask_mail import Mail, Message
from datetime import datetime
from user_management.config import Config
from sqlalchemy.exc import OperationalError  
from repositories.client import ClientRepository
from repositories.attorney import AttorneyRepository
from repositories.account import AccountRepository
from repositories.message import MessageRepository
from repositories.employee import EmployeeRepository
from repositories.lawFirm import LawFirmRepository
from repositories.admin import AdminRepository
import traceback
import re
from case_management.extensions import db  # Import the database instance


from repositories.case import CaseRepository
import os
import uuid
from werkzeug.security import generate_password_hash
import stripe
from case_management.models import PendingAttorneyRegistration, db


# Stripe config for Sandbox
STRIPE_PRICE_IDS = {
    "monthly": "price_1RgzVbQ1Wnoja5RvhNu2QNsu",    # $9.99/month
    "6months": "price_1Rh0KhQ1Wnoja5RvyqM5CAP3",    # $14.99/6 months
    "yearly": "price_1Rh0KrQ1Wnoja5RvxXB4j5K7"      # $17.99/year
}



# Get the project root folder
base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
template_dir = os.path.join(base_dir, 'templates')  # Templates folder

app = Flask(__name__, template_folder=template_dir)  # Flask app setup
app.config.from_object(Config)  # Load config

auth = Blueprint('auth', __name__)  # Create authentication blueprint


# Initialize a serializer for token generation
serializer = URLSafeTimedSerializer(app.config['SECRET_KEY'])

def generate_reset_token(email):
    return serializer.dumps(email, salt=app.config['SECURITY_PASSWORD_SALT'])

def confirm_reset_token(token, expiration=3600):
    try:
        email = serializer.loads(token, salt=app.config['SECURITY_PASSWORD_SALT'], max_age=expiration)
    except (SignatureExpired, BadSignature):
        return None
    return email


@auth.route('/')
def home():
    return render_template('login.html')



@auth.route('/login', methods=['GET', 'POST'])
def login():
    # Handle GET request with username parameter
    if request.method == 'GET':
        username = request.args.get('username')
        message = request.args.get('message')
        return render_template('login.html', 
                            prefilled_username=username or '',
                            message=message)

    try:
        data = request.get_json() if request.is_json else request.form
        username = data.get("username")
        password = data.get("password")

        if not username or not password:
            return redirect(url_for('auth.login', 
                                  username=username,
                                  message="Username and password are required"))

        print(f"🔍 Attempting login for username: {username}")

        # Check if user is a client
        client = ClientRepository.get_client_by_username(username)
        if client and client.check_password(password):
            session.update({
                'user_id': client.id,
                'username': client.username,
                'user_role': 'client',
                'client_id': client.client_id,
                'law_firm_id': client.law_firm_id,
                'permanent': True
            })
            return redirect(url_for('auth.client_dashboard'))

        # Check if user is an attorney
        attorney = AttorneyRepository.get_attorney_by_username(username)
        if attorney and attorney.check_password(password):
            if attorney.payment_status != 'paid':
                return redirect(url_for('auth.login',
                                      username=username,
                                      message="Payment not completed. Please complete payment to access your account."))
            attorney.last_login = datetime.utcnow()
            db.session.commit()
            session.update({
                'user_id': attorney.id,
                'username': attorney.username,
                'user_role': 'attorney',
                'attorney_id': attorney.attorney_id,
                'law_firm_id': attorney.law_firm_id,
                'permanent': True
            })
            return redirect(url_for('auth.attorney_dashboard'))

        # Check if user is an employee
        employee = EmployeeRepository.get_employee_by_username(username)
        if employee and employee.check_password(password):
            employee.last_login = datetime.utcnow()
            db.session.commit()
            session.update({
                'user_id': employee.id,
                'username': employee.username,
                'user_role': 'employee',
                'employee_id': employee.id,
                'law_firm_id': employee.law_firm_id,
                'permanent': True
            })
            return redirect(url_for('auth.employee_dashboard'))

        admin = AdminRepository.get_admin_by_username(username)
        if admin and admin.check_password(password):
            admin.last_login = datetime.utcnow()
            db.session.commit()
            session.update({
                'user_id': admin.id,
                'username': admin.username,
                'user_role': 'admin',
                'admin_id': admin.id,
                'permanent': True
            })
            return redirect("/admin/permissions")
        
        return redirect(url_for('auth.login',
                              username=username,
                              message="Invalid username or password"))

    except Exception as e:
        return redirect(url_for('auth.login',
                              message=f"An error occurred: {str(e)}"))

@auth.route('/register', methods=['GET'])
def register():
    # Get the selected role from the query parameters
    role = request.args.get('role')

    # Redirect to the appropriate registration form based on the role
    if role == 'client':
        return redirect(url_for('auth.register_client'))
    elif role == 'attorney':
        return redirect(url_for('auth.register_attorney'))
    elif role == 'employee':
        return redirect(url_for('auth.add_employee'))
    else:
        return jsonify({"error": "Invalid role"}), 400


def generate_unique_client_id():
    # Generate a unique client ID, e.g., "CL" followed by 8 uppercase hexadecimal characters
    return "CL" + uuid.uuid4().hex[:8].upper()

@auth.route('/api/check-username')
def check_username():
    username = request.args.get('username')
    if not username:
        return jsonify({"available": False}), 400
    
    exists = ClientRepository.get_client_by_username(username) is not None
    return jsonify({"available": not exists})

@auth.route('/api/check-email')
def check_email():
    email = request.args.get('email')
    if not email:
        return jsonify({"available": False}), 400
    
    exists = ClientRepository.get_client_by_email(email) is not None
    return jsonify({"available": not exists})

@auth.route('/register/client', methods=['GET','POST'])
def register_client():
    if request.method == 'GET':
        return render_template('register_client.html')
    
    try:
        # Get registration data - handle both JSON and form data
        data = request.get_json() if request.is_json else request.form.to_dict()

        # Validate required fields
        required_fields = ['name', 'username', 'password', 'email']
        missing_fields = [field for field in required_fields if not data.get(field)]
        if missing_fields:
            return jsonify({
                "status": "error",
                "message": f"Missing required fields: {', '.join(missing_fields)}"
            }), 400

        # Check if username or email exists
        if ClientRepository.get_client_by_username(data['username']):
            return jsonify({
                "status": "error",
                "message": "Username already exists",
                "field": "username"
            }), 409

        if ClientRepository.get_client_by_email(data['email']):
            return jsonify({
                "status": "error",
                "message": "Email already registered",
                "field": "email"
            }), 409

        # Create new client
        client_id = f"C{uuid.uuid4().hex[:8].upper()}"
        new_client = ClientRepository.create_client(
            client_id=client_id,
            name=data['name'],
            username=data['username'],
            password_hash=generate_password_hash(data['password']),
            email=data['email'],
            phone_number=data.get('phone_number', ''),
            address=data.get('address', ''),
            description=data.get('description', ''),
            law_firm_id=None,
            primary_attorney_id=None,
            collaborating_attorneys=None,
            custom_fields=None
        )

        if not new_client:
            return jsonify({
                "status": "error",
                "message": "Failed to create client"
            }), 500

        # Set session data properly
        session.update({
            'user_id': new_client.id,
            'username': new_client.username,
            'user_role': 'client',
            'client_id': new_client.client_id,
            'law_firm_id': new_client.law_firm_id,
            'permanent': True,
            '_fresh': True  # Mark session as fresh
        })
        
        # Explicitly commit the session
        session.modified = True
        db.session.commit()

        # For AJAX requests
        if request.is_json:
            return jsonify({
                "status": "success",
                "message": "Registration successful",
                "redirect": url_for('auth.client_dashboard')
            }), 201

        # For form submissions
        return redirect(url_for('auth.client_dashboard'))

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500


@auth.route('/register/admin', methods=['POST'])
def register_admin():
    try:
        data = request.get_json() if request.is_json else request.form
        name = data.get("name")
        username = data.get("username")
        password = data.get("password")
        email = data.get("email")
        phone = data.get("phone_number")

        if not all([name, username, password, email]):
            return jsonify({"error": "Missing required fields"}), 400

        from werkzeug.security import generate_password_hash
        password_hash = generate_password_hash(password)

        from repositories.admin import AdminRepository
        new_admin = AdminRepository.create_admin(
            name=name,
            username=username,
            password_hash=password_hash,
            email=email,
            phone_number=phone
        )

        if not new_admin:
            return jsonify({"error": "Error creating admin"}), 500

        return jsonify({"message": "Admin registered successfully", "admin_id": new_admin.id}), 201

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@auth.route('/register/attorney', methods=['GET', 'POST'])
def register_attorney():
    if request.method == 'GET':
        return render_template('attorney_registration_with_payment.html')

    elif request.method == 'POST':
        try:
            data = request.get_json() or request.form
            # Extract all required fields
            username = data.get("username")
            password = data.get("password")
            name = data.get("name")
            email = data.get("email")
            phone_number = data.get("phone_number")
            address = data.get("address")
            specialization = data.get("specialization")
            description = data.get("description")
            role = data.get("role")
            hourly_rate = data.get("hourly_rate")
            law_firm_id = data.get("law_firm_id")

            # Convert hourly_rate to float if it's a string
            try:
                if hourly_rate:
                    hourly_rate = float(hourly_rate)
            except (ValueError, TypeError):
                return jsonify({"error": "Invalid hourly rate format"}), 400

            # Validate required fields
            if not all([username, password, name, email, role, hourly_rate, law_firm_id]):
                return jsonify({"error": "Missing required fields"}), 400

            # Validate law_firm_id is an integer
            try:
                law_firm_id_int = int(law_firm_id)
            except (ValueError, TypeError):
                return jsonify({"error": "Law firm ID must be a valid number"}), 400
            law_firm_id = law_firm_id_int

            # (Stripe integration logic for law firm validation should follow here, not the old HEAD logic)

            # Check if the law firm exists
            law_firm = LawFirmRepository.get_law_firm_by_id(law_firm_id)
            if not law_firm:
                return jsonify({"error": "Invalid law firm ID"}), 400

            # Check if the username is already taken
            if AttorneyRepository.get_attorney_by_username(username):
                return jsonify({"error": "Username already taken"}), 400

            # Check if the attorney is already registered (e.g., by email)
            if AttorneyRepository.get_attorney_by_email(email):
                return jsonify({"error": "An attorney with this email is already registered"}), 400

            # Hash the password
            password_hash = generate_password_hash(password)

            # Create the new attorney using the repository method
            new_attorney = AttorneyRepository.create_attorney(
                username=username,
                password_hash=password_hash,
                name=name,
                email=email,
                specialization=specialization,
                description=description,
                phone_number=phone_number,
                address=address,
                role=role,
                hourly_rate=hourly_rate,
                law_firm_id=law_firm_id
            )

            # If the repository method fails, return an error
            if not new_attorney:
                return jsonify({"error": "Error creating attorney"}), 500

            # Return success response
            return jsonify({"message": f"Attorney registration successful! Your attorney ID is: {new_attorney.attorney_id}"}), 201

        except Exception as e:
            return jsonify({"error": f"An unexpected error occurred: {str(e)}"}), 500
        

@auth.route('/register/law-firm', methods=['GET', 'POST'])
def register_law_firm():
    """
    Handles both rendering the form (GET) and processing form submissions (POST).
    """
    if request.method == 'GET':
        # Render the form for GET requests
        return render_template('register_lawfirm.html')

    elif request.method == 'POST':
        # Process the form data for POST requests
        try:
            try:
                data = request.get_json(force=False, silent=True)
                if not data:
                    data = request.form
            except:
                data = request.form
            name = data.get("name")
            address = data.get("address")
            contact_email = data.get("contact_email")
            phone_number = data.get("phone_number")

            if not name or not contact_email or not address or not phone_number:
                return jsonify({"error": "Name, address, contact email, and phone number are required"}), 400

            # Check if the law firm already exists
            if LawFirmRepository.get_law_firm_by_name(name):
                return jsonify({"error": "Law firm already registered"}), 400

            # Create the law firm
            new_law_firm = LawFirmRepository.create_law_firm(
                name=name,
                address=address,
                contact_email=contact_email,
                phone_number=phone_number
            )

            if not new_law_firm:
                return jsonify({"error": "Error creating law firm"}), 500

            return jsonify({
                "message": "Law firm registered successfully",
                "law_firm_id": new_law_firm.id
            }), 201

        except Exception as e:
            return jsonify({"error": f"An unexpected error occurred: {str(e)}"}), 500

@auth.route('/validate-law-firm/<string:law_firm_name>', methods=['GET'])
def validate_law_firm(law_firm_name):
    """
    Validates whether the provided law firm name exists in the database.
    """
    print("validate_law_firm called with:", law_firm_name)  # Debugging log
    try:
        # Check if the law firm exists by name
        law_firm = LawFirmRepository.get_law_firm_by_name(law_firm_name)
        if not law_firm:
            return jsonify({"error": "Law firm not found"}), 404

        return jsonify({
            "message": "Law firm is valid",
            "law_firm_id": law_firm.id,
            "law_firm_name": law_firm.name
        }), 200

    except Exception as e:
        return jsonify({"error": f"An unexpected error occurred: {str(e)}"}), 500
    
@auth.route('/validate-law-firm-id/<int:law_firm_id>', methods=['GET'])
def validate_law_firm_id(law_firm_id):
    try:
        law_firm = LawFirmRepository.get_law_firm_by_id(law_firm_id)
        if not law_firm:
            return jsonify({"error": "Law firm ID not found"}), 404

        return jsonify({
            "message": "Law firm ID is valid",
            "law_firm_id": law_firm.id,
            "law_firm_name": law_firm.name
        }), 200
    except Exception as e:
        return jsonify({"error": f"Unexpected error: {str(e)}"}), 500

@auth.route('/api/law-firms/search', methods=['GET'])
def search_law_firms():
    """
    Search for law firms by name. Supports partial matching.
    Query parameter: q (search query)
    """
    try:
        query = request.args.get('q', '').strip()
        if not query:
            # Return all law firms if no query
            all_firms = LawFirmRepository.get_all_law_firms()
            return jsonify({
                "firms": [{"id": firm.id, "name": firm.name} for firm in all_firms]
            }), 200

        # Search for matching firms
        matching_firms = LawFirmRepository.search_law_firms_by_name(query)
        return jsonify({
            "firms": [{"id": firm.id, "name": firm.name} for firm in matching_firms],
            "query": query
        }), 200

    except Exception as e:
        return jsonify({"error": f"Search failed: {str(e)}"}), 500
    
from flask import render_template

@auth.route('/register/employee', methods=['GET', 'POST'])
def add_employee():
    if request.method == 'GET':
        return render_template('register_employee.html')

    elif request.method == 'POST':
        try:
            data = request.json if request.is_json else request.form
            print("Received data:", data)

            required_fields = {
                'name': data.get('name'),
                'email': data.get('email'),
                'username': data.get('username'),
                'password': data.get('password'),
                'role': data.get('role'),
                'hourly_rate': data.get('hourly_rate'),
                'law_firm_name': data.get('law_firm_name')
            }

            missing_fields = [field for field, value in required_fields.items() if not value]
            if missing_fields:
                return jsonify({
                    "status": "error",
                    "message": f"Missing required fields: {', '.join(missing_fields)}",
                    "missing_fields": missing_fields
                }), 400

            law_firm = LawFirmRepository.get_law_firm_by_name(required_fields['law_firm_name'])
            if not law_firm:
                # Get available law firms to show in error message
                available_firms = LawFirmRepository.get_all_law_firms()
                firm_names = [firm.name for firm in available_firms] if available_firms else []

                error_message = f"Law firm '{required_fields['law_firm_name']}' not found."
                if firm_names:
                    error_message += f" Available law firms: {', '.join(firm_names)}"
                else:
                    error_message += " No law firms are registered in the system."

                return jsonify({
                    "status": "error",
                    "message": error_message,
                    "available_firms": firm_names
                }), 404

            if EmployeeRepository.get_employee_by_email(required_fields['email']):
                return jsonify({
                    "status": "error",
                    "message": "Email already registered. Please use a different email."
                }), 400

            if EmployeeRepository.get_employee_by_username(required_fields['username']):
                return jsonify({
                    "status": "error",
                    "message": "Username already taken. Please choose another."
                }), 400

            try:
                hourly_rate = float(required_fields['hourly_rate'])
            except ValueError:
                return jsonify({
                    "status": "error",
                    "message": "Invalid hourly rate. Please enter a valid number."
                }), 400

            password_hash = generate_password_hash(required_fields['password'])

            employee = EmployeeRepository.create_employee(
                name=required_fields['name'],
                email=required_fields['email'],
                username=required_fields['username'],
                password_hash=password_hash,
                role=required_fields['role'],
                hourly_rate=hourly_rate,
                law_firm_id=law_firm.id,
                phone_number=data.get('phone_number')
            )

            if not employee:
                return jsonify({
                    "status": "error",
                    "message": "Failed to create employee. Please try again."
                }), 500

            # Return the employee ID for the frontend to use
            return jsonify({
                "status": "success",
                "message": "Registration successful!",
                "employee_id": employee.id
            }), 201

        except Exception as e:
            print(f"Error in employee registration: {str(e)}")
            traceback.print_exc()
            return jsonify({
                "status": "error",
                "message": "An unexpected error occurred. Please try again later."
            }), 500
        
@auth.route('/forgot-password', methods=['GET', 'POST'])
def forgot_password():
     if request.method == 'POST':
        # Use JSON data if available, otherwise use form data
        data = request.get_json() if request.is_json else request.form
        email = data.get('email')
        if not email:
            flash("Please enter your email address.", "error")
            return render_template('forgot_password.html')
        
        # Lookup user from both clients and attorneys
        user, user_type = AccountRepository.get_user_by_email(email)
        if not user:
            flash("No account found with that email address.", "error")
            return render_template('forgot_password.html')
        
        token = generate_reset_token(email)
        reset_url = url_for('auth.reset_password', token=token, _external=True)
        
        subject = "Password Reset Requested"
        sender = app.config['MAIL_USERNAME']
        recipients = [email]
        body = f"Hi,\n\nTo reset your password, click the following link:\n{reset_url}\n\nIf you did not request a password reset, please ignore this email."
        msg = Message(subject, sender=sender, recipients=recipients, body=body)

         # Retrieve the mail instance from current_app's extensions
        mail = current_app.extensions.get('mail')
        if mail:
            mail.send(msg)
            flash("A password reset link has been sent to your email.", "info")

        else:
            current_app.logger.error("Flask-Mail instance not found in current_app.extensions")
        
        #flash("A password reset link has been sent to your email.", "info")
        #return redirect(url_for('auth.home'))
    
     return render_template('forgot_password.html')

@auth.route('/reset-password/<token>', methods=['GET', 'POST'])
def reset_password(token):
    email = confirm_reset_token(token)
    if not email:
        flash("The password reset link is invalid or has expired.", "error")
        return redirect(url_for('forgot_password'))
    
    user, user_type = AccountRepository.get_user_by_email(email)
    if not user:
        flash("User not found.", "error")
        return redirect(url_for('forgot_password'))
    
    if request.method == 'POST':
         # Use JSON data if available; otherwise, use form data
        data = request.get_json() if request.is_json else request.form
        new_password = data.get('password')
        if not new_password:
            flash("Please enter a new password.", "error")
            return render_template('reset_password.html', token=token)
        
        if AccountRepository.update_user_password(user, new_password):
            flash("Your password has been updated successfully!", "success")
            return render_template('password_updated.html')
        else:
            flash("Error updating your password. Please try again.", "error")
            return render_template('reset_password.html', token=token)

    return render_template('reset_password.html', token=token)

  
  
@auth.route('/check-session', methods=['GET'])
def check_session():
    if 'username' in session:
        return jsonify({
            "username": session.get('username'),
            "user_role": session.get('user_role'),
            "client_id": session.get('client_id'),
            "attorney_id": session.get('attorney_id')
        })
    return jsonify({"error": "No active session"}), 401


@auth.route('/login/attorney/session', methods=['POST'])
def login_attorney_session():
    username = request.json.get('username')
    from repositories.attorney import AttorneyRepository
    attorney = AttorneyRepository.get_attorney_by_username(username)
    if not attorney or attorney.payment_status != 'paid':
        return jsonify({'error': 'Invalid or unpaid attorney.'}), 400
    session['user_id'] = attorney.id
    session['username'] = attorney.username
    session['user_role'] = 'attorney'
    session['attorney_id'] = attorney.attorney_id
    return jsonify({'success': True})

@auth.route('/attorney_dashboard')
def attorney_dashboard():
    if not has_role('attorney'):
        return "Access denied. Attorneys only.", 403
    
    # Fetch the logged-in attorney's details
    attorney = AttorneyRepository.get_attorney_by_username(session['username'])
    if not attorney:
        return redirect(url_for('login'))  # Redirect if attorney is not found

    # Render the attorney dashboard template
    return render_template('attorney_dashboard.html', username=attorney.name)

@auth.route('/client_dashboard')
def client_dashboard():
    if not has_role('client'):
        return "Access denied. Clients only.", 403
    
    # Fetch the logged-in client's details
    client = ClientRepository.get_client_by_username(session.get('username'))
    if not client:
        return redirect(url_for('auth.login'))  # Redirect if client not found


    # Log the client name before rendering the template
    print(f"Rendering dashboard for Client Name: {client.name}")
    
    # Render the client dashboard template
    return render_template('client_dashboard.html', username=client.name)

# @auth.route('/employee-dashboard')
# def employee_dashboard():
#     if session.get('user_role') != 'employee':
#         return redirect(url_for('auth.login'))  # Redirect unauthorized users

#     return render_template('attorney_dashboard.html')

@auth.route('/messaging', methods=['GET', 'POST'])
def messaging():
    if 'username' not in session:
        return redirect(url_for('auth.login'))

    if session.get('user_role') not in ['attorney', 'client']:
        return "Access denied. Messaging is only available for attorneys and clients.", 403

    case_id = request.args.get('case_id') or request.form.get('case_id')
    if session['user_role'] == 'client':
        client_cases = CaseRepository.get_cases_by_client(session.get('client_id'))
        if case_id and case_id not in [case.case_id for case in client_cases]:
            return "Access denied. You can only access your own cases.", 403

    if request.method == 'POST':
        message_content = request.form['message']
        sender = session['username']
        MessageRepository.create_message(case_id, sender, message_content)

    messages = MessageRepository.get_messages_by_case(case_id) if case_id else []
    return render_template('messaging.html', messages=messages, case_id=case_id)

@auth.route('/logout')
def logout():
    print("Logging out...")  # Debugging log
    session.clear()  # Clear the session
    return redirect(url_for('auth.home'))  # Redirect to the home route

@auth.route('/create-attorney-checkout-session', methods=['POST'])
def create_attorney_checkout_session():
    stripe.api_key = current_app.config.get('STRIPE_SECRET_KEY')
    data = request.json
    plan = data.get('plan')
    registration_data = data.get('registration_data')

    if plan not in STRIPE_PRICE_IDS:
        return jsonify({"error": "Invalid plan selected"}), 400

    if not registration_data:
        return jsonify({"error": "Missing registration data"}), 400

    try:
        checkout_session = stripe.checkout.Session.create(
            payment_method_types=['card'],
            line_items=[{
                'price': STRIPE_PRICE_IDS[plan],
                'quantity': 1,
            }],
            mode='subscription',
            success_url=url_for('auth.attorney_payment_success_page', _external=True) + '?session_id={CHECKOUT_SESSION_ID}',
            cancel_url=url_for('auth.register_attorney', _external=True),
        )
    except Exception as e:
        return jsonify({"error": f"Stripe error: {str(e)}"}), 500

    # Store registration data in DB
    pending = PendingAttorneyRegistration(
        stripe_session_id=checkout_session.id,
        registration_data=registration_data
    )
    db.session.add(pending)
    db.session.commit()
    return jsonify({'checkout_url': checkout_session.url})

@auth.route('/attorney-payment-success')
def attorney_payment_success():
    session_id = request.args.get('session_id')
    print(f"[DEBUG] Raw session_id from URL: {repr(session_id)} (type: {type(session_id)})")
    session_id = session_id.strip() if session_id else session_id
    print(f"[DEBUG] Stripped session_id: {repr(session_id)} (type: {type(session_id)})")
    # Print all session_ids in the DB for comparison
    all_pending_objs = PendingAttorneyRegistration.query.all()
    print(f"[DEBUG] All session_ids in DB:")
    for obj in all_pending_objs:
        print(f"  id={obj.id}, session_id={repr(obj.stripe_session_id)} (type: {type(obj.stripe_session_id)}), completed={obj.completed}")
        if obj.stripe_session_id and session_id and obj.stripe_session_id.strip() == session_id:
            print(f"  [MATCH] This session_id matches the incoming session_id!")
    try:
        stripe.api_key = current_app.config.get('STRIPE_SECRET_KEY')
        stripe_session = stripe.checkout.Session.retrieve(session_id)
        print(f"[DEBUG] Stripe session payment_status: {stripe_session.payment_status}")
        print(f"[DEBUG] Stripe session status: {stripe_session.status}")
        # Query with stripped session_id
        pending = PendingAttorneyRegistration.query.filter_by(stripe_session_id=session_id, completed=False).first()
        if not pending:
            print("[DEBUG] No pending registration found for this session_id and completed=False")
            # Redirect to registration page for test compatibility
            return redirect(url_for('auth.register_attorney'))
        print(f"[DEBUG] Pending registration found: {pending}")
        registration_data = pending.registration_data
        print(f"[DEBUG] Registration data: {registration_data}")
        # Extract fields
        username = registration_data.get("username")
        password = registration_data.get("password")
        name = registration_data.get("name")
        email = registration_data.get("email")
        phone_number = registration_data.get("phone_number")
        address = registration_data.get("address")
        specialization = registration_data.get("specialization")
        description = registration_data.get("description")
        role = registration_data.get("role")
        hourly_rate = registration_data.get("hourly_rate")
        law_firm_id = registration_data.get("law_firm_id")
        password_hash = generate_password_hash(password)
        new_attorney = AttorneyRepository.create_attorney(
            username=username,
            password_hash=password_hash,
            name=name,
            email=email,
            specialization=specialization,
            description=description,
            phone_number=phone_number,
            address=address,
            role=role,
            hourly_rate=hourly_rate,
            law_firm_id=law_firm_id,
            payment_status='paid',
            stripe_session_id=session_id
        )
        if not new_attorney:
            flash("Error creating attorney. Please contact support.", "error")
            return redirect(url_for('auth.register_attorney'))
        # Mark as completed
        pending.completed = True
        db.session.commit()
        # Set session for new attorney
        session['user_id'] = new_attorney.id
        session['username'] = new_attorney.username
        session['user_role'] = 'attorney'
        session['attorney_id'] = new_attorney.attorney_id
        flash("Payment successful! Registration complete.", "success")
        return render_template('attorney_registration_success.html')
    except Exception as e:
        flash(f"Error processing payment: {str(e)}", "error")
        return redirect(url_for('auth.register_attorney'))

@auth.route('/test-success-page')
def test_success_page():
    """Test route to verify the success page works"""
    return render_template('attorney_registration_success.html')

@auth.route('/register/attorney/success')
def attorney_payment_success_page():
    session_id = request.args.get('session_id')
    return render_template('attorney_payment_verification.html', session_id=session_id)

@auth.route('/register/attorney/confirm', methods=['POST'])
def confirm_attorney_registration():
    session_id = request.json.get('session_id')
    if not session_id:
        return jsonify({'error': 'Missing session_id.'}), 400
    try:
        stripe.api_key = current_app.config['STRIPE_SECRET_KEY']
        stripe_session = stripe.checkout.Session.retrieve(session_id)
        if stripe_session.payment_status != 'paid':
            return jsonify({'error': 'Payment not completed.'}), 400
        pending = PendingAttorneyRegistration.query.filter_by(stripe_session_id=session_id, completed=False).first()
        if not pending:
            # Check if attorney already created for this session (idempotency)
            from case_management.models import Attorney
            existing = Attorney.query.filter_by(stripe_session_id=session_id, payment_status='paid').first()
            if existing:
                return jsonify({'is_premium': True, 'status': 'active', 'username': existing.username})
            else:
                return jsonify({'error': 'No attorney found for this session. Please contact support.'}), 404
        data = pending.registration_data
        # Check for duplicate username/email
        if AttorneyRepository.get_attorney_by_username(data['username']) or AttorneyRepository.get_attorney_by_email(data['email']):
            pending.completed = True
            db.session.commit()
            return jsonify({'error': 'Username or email already exists.'}), 400
        new_attorney = AttorneyRepository.create_attorney(
            username=data['username'],
            password_hash=generate_password_hash(data['password']),
            name=data['name'],
            email=data['email'],
            specialization=data.get('specialization'),
            description=data.get('description'),
            phone_number=data.get('phone_number'),
            address=data.get('address'),
            role=data.get('role'),
            hourly_rate=data.get('hourly_rate'),
            law_firm_id=data.get('law_firm_id'),
            payment_status='paid',
            stripe_session_id=session_id
        )
        pending.completed = True
        db.session.commit()
        return jsonify({'detail': 'Registration confirmed', 'is_premium': True, 'status': 'active', 'username': new_attorney.username})
    except Exception as e:
        return jsonify({'error': f'Error confirming registration: {str(e)}'}), 500

# Test-only login route for Cypress/CI
if os.environ.get('FLASK_ENV') == 'development' or os.environ.get('CI') == 'true':
    @auth.route('/test-login/attorney', methods=['POST'])
    def test_login_attorney():
        # Set up a fake attorney session for Cypress tests
        session['user_id'] = 99999
        session['username'] = 'test_attorney'
        session['user_role'] = 'attorney'
        session['attorney_id'] = 'ATTEST1234'
        session['law_firm_id'] = 1
        return jsonify({'message': 'Test attorney session set'}), 200

@auth.route('/mobile/login', methods=['POST'])
def mobile_login():
    try:
        data = request.get_json()
        username = data.get("username")
        password = data.get("password")

        if not username or not password:
            return jsonify({"success": False, "error": "Username and password are required"}), 400

        # Check if user is a client
        client = ClientRepository.get_client_by_username(username)
        if client and client.check_password(password):
            session.update({
                'user_id': client.id,
                'username': client.username,
                'user_role': 'client',
                'client_id': client.client_id,
                'law_firm_id': client.law_firm_id,  # Store law firm ID
                'permanent': True
            })
            return jsonify({
                "success": True,
                "message": "Login successful",
                "user": {
                    "id": client.id,
                    "username": client.username,
                    "role": "client",
                    "client_id": client.client_id,
                    "law_firm_id": client.law_firm_id,
                    "name": client.name,
                }
            })

        # Similar checks for other roles if needed
        return jsonify({"success": False, "error": "Invalid username or password"}), 401

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True)
    
@auth.route('/employee/dashboard')
def employee_dashboard():
    # Check for new employee parameter first (allows temporary access)
    new_employee_id = request.args.get('new_employee')
    
    if new_employee_id:
        # Get the new employee
        employee = EmployeeRepository.get_employee_by_id(new_employee_id)
        if employee:
            # Set the session for this new employee
            session.update({
                'user_id': employee.id,
                'username': employee.username,
                'user_role': 'employee',
                'employee_id': employee.id,
                'law_firm_id': employee.law_firm_id,
                'permanent': True
            })
            return render_template('employee_dashboard.html', employee=employee)
    
    # Normal session check for returning employees
    if 'user_id' not in session or session.get('user_role') != 'employee':
        return redirect(url_for('auth.login'))
    
    employee = EmployeeRepository.get_employee_by_id(session['employee_id'])
    if not employee:
        flash('Employee not found.', 'error')
        return redirect(url_for('auth.login'))
    
    return render_template('employee_dashboard.html', employee=employee)