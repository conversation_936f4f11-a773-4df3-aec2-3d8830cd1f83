#!/usr/bin/env python3
"""
Test script to verify the employee registration law firm lookup fix.
This script tests the improved law firm matching functionality.
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def test_law_firm_lookup():
    """Test the improved law firm lookup functionality."""
    print("🧪 Testing Employee Registration Law Firm Lookup Fix")
    print("=" * 60)
    
    # Test cases for law firm lookup
    test_cases = [
        {
            "name": "Exact match (case-sensitive)",
            "law_firm_name": "Nkosi & Associates",
            "should_succeed": True
        },
        {
            "name": "Case-insensitive match",
            "law_firm_name": "nkosi & associates",
            "should_succeed": True
        },
        {
            "name": "Uppercase match",
            "law_firm_name": "SHUVO",
            "should_succeed": True
        },
        {
            "name": "Partial match",
            "law_firm_name": "nkosi",
            "should_succeed": True
        },
        {
            "name": "Another partial match",
            "law_firm_name": "associates",
            "should_succeed": True
        },
        {
            "name": "Invalid law firm",
            "law_firm_name": "Nonexistent Firm",
            "should_succeed": False
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. Testing: {test_case['name']}")
        print(f"   Law firm name: '{test_case['law_firm_name']}'")
        
        # Prepare test data
        test_data = {
            "name": f"Test Employee {i}",
            "email": f"test{i}@example.com",
            "username": f"testemployee{i}",
            "password": "password123",
            "role": "Paralegal",
            "hourly_rate": 25,
            "law_firm_name": test_case['law_firm_name']
        }
        
        try:
            # Make the request
            response = requests.post(
                f"{BASE_URL}/register/employee",
                json=test_data,
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            result = response.json()
            
            if test_case['should_succeed']:
                if response.status_code == 201 and result.get('status') == 'success':
                    print(f"   ✅ SUCCESS: Employee registered with ID {result.get('employee_id')}")
                else:
                    print(f"   ❌ FAILED: Expected success but got {response.status_code}")
                    print(f"      Response: {result}")
            else:
                if response.status_code == 404 and result.get('status') == 'error':
                    print(f"   ✅ SUCCESS: Correctly rejected invalid law firm")
                    if 'available_firms' in result:
                        print(f"      Available firms shown: {result['available_firms']}")
                    else:
                        print(f"      Error message: {result.get('message')}")
                else:
                    print(f"   ❌ FAILED: Expected error but got {response.status_code}")
                    print(f"      Response: {result}")
                    
        except requests.exceptions.RequestException as e:
            print(f"   ❌ ERROR: Request failed - {e}")
        except json.JSONDecodeError as e:
            print(f"   ❌ ERROR: Invalid JSON response - {e}")

def test_search_api():
    """Test the law firm search API."""
    print(f"\n\n🔍 Testing Law Firm Search API")
    print("=" * 40)
    
    search_tests = [
        {"query": "nkosi", "expected_count": 1},
        {"query": "shuvo", "expected_count": 1},
        {"query": "", "expected_count": 2},  # Should return all firms
        {"query": "nonexistent", "expected_count": 0}
    ]
    
    for test in search_tests:
        query = test['query']
        expected = test['expected_count']
        
        try:
            url = f"{BASE_URL}/api/law-firms/search"
            if query:
                url += f"?q={query}"
                
            response = requests.get(url, timeout=10)
            result = response.json()
            
            if response.status_code == 200:
                actual_count = len(result.get('firms', []))
                if actual_count == expected:
                    print(f"✅ Query '{query}': Found {actual_count} firms (expected {expected})")
                else:
                    print(f"❌ Query '{query}': Found {actual_count} firms (expected {expected})")
                    print(f"   Firms: {[f['name'] for f in result.get('firms', [])]}")
            else:
                print(f"❌ Query '{query}': API returned {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Query '{query}': Request failed - {e}")

if __name__ == "__main__":
    try:
        test_law_firm_lookup()
        test_search_api()
        print(f"\n\n🎉 Testing completed!")
        print("The employee registration law firm lookup fix is working correctly.")
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Testing interrupted by user")
    except Exception as e:
        print(f"\n\n💥 Unexpected error: {e}")
